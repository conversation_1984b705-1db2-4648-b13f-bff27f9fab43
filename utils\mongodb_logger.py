"""
MongoDB Logging Utility for RTRDA Project

This module provides logging functionality specifically for MongoDB operations
with structured logging and automatic log storage to MongoDB.
"""

import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from django.conf import settings
from .mongodb_crud import MongoDBCRUD
from .mongodb_exceptions import MongoDBBaseException


class MongoDBLogHandler(logging.Handler):
    """
    Custom logging handler that stores logs in MongoDB.
    """
    
    def __init__(self, collection_name: str = 'application_logs'):
        super().__init__()
        self.collection_name = collection_name
        self.crud = None
        self._initialize_crud()
    
    def _initialize_crud(self):
        """Initialize MongoDB CRUD operations."""
        try:
            self.crud = MongoDBCRUD(self.collection_name)
        except Exception as e:
            # Fallback to console logging if MongoDB is not available
            print(f"Failed to initialize MongoDB logging: {e}")
            self.crud = None
    
    def emit(self, record):
        """Emit a log record to MongoDB."""
        if not self.crud:
            return
        
        try:
            log_document = self._format_log_record(record)
            self.crud.create_one(log_document)
        except Exception as e:
            # Avoid infinite recursion by not logging this error
            print(f"Failed to store log in MongoDB: {e}")
    
    def _format_log_record(self, record) -> Dict[str, Any]:
        """Format log record for MongoDB storage."""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line_number': record.lineno,
            'pathname': record.pathname,
            'process_id': record.process,
            'thread_id': record.thread,
            'thread_name': record.threadName,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields from the log record
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                extra_fields[key] = value
        
        if extra_fields:
            log_data['extra'] = extra_fields
        
        return log_data


class MongoDBLogger:
    """
    MongoDB-specific logger with enhanced functionality.
    """
    
    def __init__(self, name: str = 'mongodb_operations'):
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with MongoDB handler."""
        if not self.logger.handlers:
            # Add MongoDB handler
            mongodb_handler = MongoDBLogHandler()
            mongodb_handler.setLevel(logging.INFO)
            
            # Add console handler for development
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            
            # Create formatter
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            
            # Add handlers to logger
            self.logger.addHandler(mongodb_handler)
            self.logger.addHandler(console_handler)
            self.logger.setLevel(logging.DEBUG)
    
    def log_operation(self, operation: str, collection: str, 
                     result: Any = None, error: Exception = None,
                     extra_data: Dict[str, Any] = None):
        """
        Log MongoDB operation with structured data.
        
        Args:
            operation (str): Type of operation (create, read, update, delete)
            collection (str): Collection name
            result (Any): Operation result
            error (Exception): Error if operation failed
            extra_data (Dict[str, Any]): Additional data to log
        """
        log_data = {
            'operation': operation,
            'collection': collection,
            'timestamp': datetime.utcnow().isoformat(),
        }
        
        if extra_data:
            log_data.update(extra_data)
        
        if error:
            self.logger.error(
                f"MongoDB {operation} operation failed on collection '{collection}': {str(error)}",
                extra=log_data,
                exc_info=True
            )
        else:
            success_message = f"MongoDB {operation} operation successful on collection '{collection}'"
            if result:
                if hasattr(result, 'inserted_id'):
                    log_data['inserted_id'] = str(result.inserted_id)
                elif hasattr(result, 'matched_count'):
                    log_data['matched_count'] = result.matched_count
                    log_data['modified_count'] = getattr(result, 'modified_count', 0)
                elif hasattr(result, 'deleted_count'):
                    log_data['deleted_count'] = result.deleted_count
            
            self.logger.info(success_message, extra=log_data)
    
    def log_connection_event(self, event: str, details: Dict[str, Any] = None):
        """
        Log MongoDB connection events.
        
        Args:
            event (str): Connection event type
            details (Dict[str, Any]): Event details
        """
        log_data = {
            'event_type': 'connection',
            'event': event,
            'timestamp': datetime.utcnow().isoformat(),
        }
        
        if details:
            log_data.update(details)
        
        if event in ['connection_failed', 'connection_lost']:
            self.logger.error(f"MongoDB connection event: {event}", extra=log_data)
        else:
            self.logger.info(f"MongoDB connection event: {event}", extra=log_data)
    
    def log_performance(self, operation: str, collection: str, 
                       execution_time: float, document_count: int = None):
        """
        Log MongoDB operation performance metrics.
        
        Args:
            operation (str): Operation type
            collection (str): Collection name
            execution_time (float): Execution time in seconds
            document_count (int): Number of documents processed
        """
        log_data = {
            'event_type': 'performance',
            'operation': operation,
            'collection': collection,
            'execution_time': execution_time,
            'timestamp': datetime.utcnow().isoformat(),
        }
        
        if document_count is not None:
            log_data['document_count'] = document_count
            log_data['documents_per_second'] = document_count / execution_time if execution_time > 0 else 0
        
        # Log as warning if operation is slow
        if execution_time > 5.0:  # 5 seconds threshold
            self.logger.warning(
                f"Slow MongoDB {operation} operation on '{collection}': {execution_time:.2f}s",
                extra=log_data
            )
        else:
            self.logger.info(
                f"MongoDB {operation} performance on '{collection}': {execution_time:.2f}s",
                extra=log_data
            )
    
    def log_validation_error(self, collection: str, errors: list, document_data: Dict[str, Any] = None):
        """
        Log document validation errors.
        
        Args:
            collection (str): Collection name
            errors (list): List of validation errors
            document_data (Dict[str, Any]): Document data that failed validation
        """
        log_data = {
            'event_type': 'validation_error',
            'collection': collection,
            'validation_errors': errors,
            'timestamp': datetime.utcnow().isoformat(),
        }
        
        if document_data:
            # Remove sensitive data before logging
            safe_data = {k: v for k, v in document_data.items() 
                        if k not in ['password', 'token', 'secret']}
            log_data['document_data'] = safe_data
        
        self.logger.error(
            f"Document validation failed for collection '{collection}': {errors}",
            extra=log_data
        )


# Global MongoDB logger instance
mongodb_logger = MongoDBLogger()


def log_mongodb_operation(operation: str, collection: str, 
                         result: Any = None, error: Exception = None,
                         extra_data: Dict[str, Any] = None):
    """
    Convenience function to log MongoDB operations.
    
    Args:
        operation (str): Type of operation
        collection (str): Collection name
        result (Any): Operation result
        error (Exception): Error if operation failed
        extra_data (Dict[str, Any]): Additional data to log
    """
    mongodb_logger.log_operation(operation, collection, result, error, extra_data)


def log_mongodb_performance(operation: str, collection: str, 
                           execution_time: float, document_count: int = None):
    """
    Convenience function to log MongoDB performance metrics.
    
    Args:
        operation (str): Operation type
        collection (str): Collection name
        execution_time (float): Execution time in seconds
        document_count (int): Number of documents processed
    """
    mongodb_logger.log_performance(operation, collection, execution_time, document_count)
