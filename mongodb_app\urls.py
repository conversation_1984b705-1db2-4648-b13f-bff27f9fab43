"""
URL Configuration for MongoDB App

This module defines URL patterns for MongoDB API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ComponentSrcViewSet,
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'component-srcs', ComponentSrcViewSet, basename='component-srcs')
# router.register(r'mongo-users', MongoUserViewSet, basename='mongo-users')
# router.register(r'mongo-logs', MongoLogViewSet, basename='mongo-logs')
# router.register(r'mongo-analytics', MongoAnalyticsViewSet, basename='mongo-analytics')
# router.register(r'mongo-configurations', MongoConfigurationViewSet, basename='mongo-configurations')

urlpatterns = [
    path('', include(router.urls)),
]
