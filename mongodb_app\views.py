"""
MongoDB API Views for RTRDA Project

These views provide REST API endpoints for MongoDB operations
following Django REST Framework patterns.
"""

import logging
from typing import Dict, Any, List
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.http import Http404
from bson.errors import InvalidId
from pymongo.errors import PyMongoError

from utils.mongodb_crud import MongoDBCRUD
from .models import ComponentSrc
from .serializers import (
    ComponentSrcSerializer,
)
from drf_spectacular.utils import extend_schema

logger = logging.getLogger(__name__)

@extend_schema(
    tags=["MongoDB"]
)
class BaseMongoViewSet(ViewSet):
    """
    Base ViewSet for MongoDB operations with common functionality.
    """

    serializer_class = None
    model_class = None
    collection_name = None
    permission_classes = [AllowAny]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.collection_name:
            self.crud = MongoDBCRUD(self.collection_name)

    def get_serializer(self, *args, **kwargs):
        """Get serializer instance."""
        serializer_class = self.get_serializer_class()
        kwargs.setdefault('context', self.get_serializer_context())
        return serializer_class(*args, **kwargs)

    def get_serializer_class(self):
        """Get serializer class."""
        return self.serializer_class

    def get_serializer_context(self):
        """Get serializer context."""
        return {
            'request': self.request,
            'format': self.format_kwarg,
            'view': self
        }

    def list(self, request):
        """List all documents."""
        try:
            # Get query parameters
            limit = request.query_params.get('limit')
            skip = request.query_params.get('skip')
            sort_by = request.query_params.get('sort_by', 'created_at')
            sort_order = request.query_params.get('sort_order', 'desc')

            # Build sort criteria
            sort_direction = -1 if sort_order.lower() == 'desc' else 1
            sort = [(sort_by, sort_direction)]

            # Convert string parameters to integers
            limit = int(limit) if limit and limit.isdigit() else None
            skip = int(skip) if skip and skip.isdigit() else None

            # Get documents
            documents = self.crud.find_many(
                sort=sort,
                limit=limit,
                skip=skip
            )

            # Get total count for pagination
            total_count = self.crud.count_documents()

            # Serialize documents
            serializer = self.get_serializer(documents, many=True)

            return Response({
                'results': serializer.data,
                'count': len(documents),
                'total_count': total_count,
                'limit': limit,
                'skip': skip or 0
            })

        except Exception as e:
            logger.error(f"Error listing documents from {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to retrieve documents'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request):
        """Create a new document."""
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                # Create document instance
                document = serializer.save()

                # Validate document
                if hasattr(document, 'validate'):
                    validation_errors = document.validate()
                    if validation_errors:
                        return Response(
                            {'validation_errors': validation_errors},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Save to MongoDB
                document_dict = document.to_dict()
                result = self.crud.create_one(document_dict)

                # Return created document
                created_document = self.crud.find_by_id(result.inserted_id)
                response_serializer = self.get_serializer(created_document)

                return Response(
                    response_serializer.data,
                    status=status.HTTP_201_CREATED
                )
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f"Error creating document in {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to create document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def retrieve(self, request, pk=None):
        """Retrieve a specific document."""
        try:
            document = self.crud.find_by_id(pk)
            if not document:
                raise Http404("Document not found")

            serializer = self.get_serializer(document)
            return Response(serializer.data)

        except InvalidId:
            return Response(
                {'error': 'Invalid document ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Http404:
            return Response(
                {'error': 'Document not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error retrieving document from {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to retrieve document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, pk=None):
        """Update a specific document."""
        try:
            # Check if document exists
            existing_document = self.crud.find_by_id(pk)
            if not existing_document:
                raise Http404("Document not found")

            serializer = self.get_serializer(data=request.data, partial=True)
            if serializer.is_valid():
                # Prepare update data
                update_data = {'$set': serializer.validated_data}

                # Update document
                result = self.crud.update_by_id(pk, update_data)

                if result.matched_count == 0:
                    raise Http404("Document not found")

                # Return updated document
                updated_document = self.crud.find_by_id(pk)
                response_serializer = self.get_serializer(updated_document)

                return Response(response_serializer.data)
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

        except InvalidId:
            return Response(
                {'error': 'Invalid document ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Http404:
            return Response(
                {'error': 'Document not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error updating document in {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to update document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, pk=None):
        """Delete a specific document."""
        try:
            result = self.crud.delete_by_id(pk)

            if result.deleted_count == 0:
                raise Http404("Document not found")

            return Response(status=status.HTTP_204_NO_CONTENT)

        except InvalidId:
            return Response(
                {'error': 'Invalid document ID'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Http404:
            return Response(
                {'error': 'Document not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error deleting document from {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to delete document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def bulk_create(self, request):
        """Create multiple documents."""
        try:
            if not isinstance(request.data, list):
                return Response(
                    {'error': 'Expected a list of documents'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            serializer = self.get_serializer(data=request.data, many=True)
            if serializer.is_valid():
                # Create document instances
                documents = []
                for item in serializer.validated_data:
                    document = self.model_class(**item)

                    # Validate document
                    if hasattr(document, 'validate'):
                        validation_errors = document.validate()
                        if validation_errors:
                            return Response(
                                {'validation_errors': validation_errors},
                                status=status.HTTP_400_BAD_REQUEST
                            )

                    documents.append(document.to_dict())

                # Save to MongoDB
                result = self.crud.create_many(documents)

                return Response({
                    'created_count': len(result.inserted_ids),
                    'inserted_ids': [str(id) for id in result.inserted_ids]
                }, status=status.HTTP_201_CREATED)
            else:
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f"Error bulk creating documents in {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to create documents'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search documents with filters."""
        try:
            # Build filter from query parameters
            filter_dict = {}

            # Add common search parameters
            for key, value in request.query_params.items():
                if key not in ['limit', 'skip', 'sort_by', 'sort_order']:
                    # Simple text search for string fields
                    if value:
                        filter_dict[key] = {'$regex': value, '$options': 'i'}

            # Get pagination parameters
            limit = request.query_params.get('limit')
            skip = request.query_params.get('skip')
            sort_by = request.query_params.get('sort_by', 'created_at')
            sort_order = request.query_params.get('sort_order', 'desc')

            # Build sort criteria
            sort_direction = -1 if sort_order.lower() == 'desc' else 1
            sort = [(sort_by, sort_direction)]

            # Convert string parameters to integers
            limit = int(limit) if limit and limit.isdigit() else None
            skip = int(skip) if skip and skip.isdigit() else None

            # Search documents
            documents = self.crud.find_many(
                filter_dict=filter_dict,
                sort=sort,
                limit=limit,
                skip=skip
            )

            # Get total count for pagination
            total_count = self.crud.count_documents(filter_dict)

            # Serialize documents
            serializer = self.get_serializer(documents, many=True)

            return Response({
                'results': serializer.data,
                'count': len(documents),
                'total_count': total_count,
                'filter': filter_dict,
                'limit': limit,
                'skip': skip or 0
            })

        except Exception as e:
            logger.error(f"Error searching documents in {self.collection_name}: {e}")
            return Response(
                {'error': 'Failed to search documents'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ComponentSrcViewSet(BaseMongoViewSet):
    """
    ViewSet for ComponentSrc documents.
    Provides CRUD operations for ComponentSrc data stored in MongoDB.
    """

    serializer_class = ComponentSrcSerializer
    model_class = ComponentSrc
    collection_name = 'ComponentSrc'
    
    def get_queryset(self):
        return self.crud.find_many()
    
    def get_serializer(self, *args, **kwargs):
        return super().get_serializer(*args, **kwargs)

    def list(self, request, *args, **kwargs):
        """List ComponentSrc documents with optional componentId filter."""
        logger.info(f"request list componentSrc")

        try:
            # Get query parameters
            componentId = request.query_params.get('componentId')
            limit = request.query_params.get('limit')
            skip = request.query_params.get('skip')
            sort_by = request.query_params.get('sort_by', 'created_at')
            sort_order = request.query_params.get('sort_order', 'desc')

            # Build filter criteria
            filter_dict = {}
            if componentId:
                filter_dict['componentId'] = componentId

            # Build sort criteria
            sort_direction = -1 if sort_order.lower() == 'desc' else 1
            sort = [(sort_by, sort_direction)]

            # Convert string parameters to integers
            limit = int(limit) if limit and limit.isdigit() else None
            skip = int(skip) if skip and skip.isdigit() else None

            # Get documents
            documents = self.crud.find_many(
                filter_dict=filter_dict,
                sort=sort,
                limit=limit,
                skip=skip
            )

            # Get total count for pagination
            total_count = self.crud.count_documents(filter_dict)

            logger.info(f"Found {len(documents)} componentSrc documents (total: {total_count})")

            # Serialize documents
            serializer = self.get_serializer(documents, many=True)

            return Response({
                'results': serializer.data,
                'count': len(documents),
                'total_count': total_count,
                'limit': limit,
                'skip': skip or 0
            })

        except Exception as e:
            logger.error(f"Error listing componentSrc documents: {e}")
            return Response(
                {'error': 'Failed to retrieve componentSrc documents'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
