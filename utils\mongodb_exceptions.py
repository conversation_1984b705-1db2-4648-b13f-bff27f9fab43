"""
MongoDB Custom Exceptions for RTRDA Project

This module defines custom exceptions for MongoDB operations
to provide better error handling and debugging information.
"""

from pymongo.errors import PyMongoError


class MongoDBBaseException(Exception):
    """Base exception for MongoDB operations."""
    
    def __init__(self, message: str, original_error: Exception = None):
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)
    
    def __str__(self):
        if self.original_error:
            return f"{self.message} (Original: {str(self.original_error)})"
        return self.message


class MongoDBConnectionError(MongoDBBaseException):
    """Exception raised when MongoDB connection fails."""
    pass


class MongoDBValidationError(MongoDBBaseException):
    """Exception raised when document validation fails."""
    
    def __init__(self, message: str, validation_errors: list = None, original_error: Exception = None):
        super().__init__(message, original_error)
        self.validation_errors = validation_errors or []


class MongoDBOperationError(MongoDBBaseException):
    """Exception raised when MongoDB operation fails."""
    pass


class MongoDBDocumentNotFoundError(MongoDBBaseException):
    """Exception raised when requested document is not found."""
    pass


class MongoDBDuplicateKeyError(MongoDBBaseException):
    """Exception raised when duplicate key constraint is violated."""
    pass


class MongoDBTimeoutError(MongoDBBaseException):
    """Exception raised when MongoDB operation times out."""
    pass


class MongoDBAuthenticationError(MongoDBBaseException):
    """Exception raised when MongoDB authentication fails."""
    pass


class MongoDBConfigurationError(MongoDBBaseException):
    """Exception raised when MongoDB configuration is invalid."""
    pass


def handle_pymongo_error(error: PyMongoError, operation: str = "MongoDB operation") -> MongoDBBaseException:
    """
    Convert PyMongo errors to custom MongoDB exceptions.
    
    Args:
        error (PyMongoError): The original PyMongo error
        operation (str): Description of the operation that failed
        
    Returns:
        MongoDBBaseException: Appropriate custom exception
    """
    from pymongo.errors import (
        ConnectionFailure, ServerSelectionTimeoutError, 
        DuplicateKeyError, WriteError, BulkWriteError,
        ConfigurationError, AuthenticationFailed,
        NetworkTimeout, ExecutionTimeout
    )
    
    error_message = f"{operation} failed: {str(error)}"
    
    if isinstance(error, (ConnectionFailure, ServerSelectionTimeoutError)):
        return MongoDBConnectionError(error_message, error)
    elif isinstance(error, DuplicateKeyError):
        return MongoDBDuplicateKeyError(error_message, error)
    elif isinstance(error, (WriteError, BulkWriteError)):
        return MongoDBOperationError(error_message, error)
    elif isinstance(error, ConfigurationError):
        return MongoDBConfigurationError(error_message, error)
    elif isinstance(error, AuthenticationFailed):
        return MongoDBAuthenticationError(error_message, error)
    elif isinstance(error, (NetworkTimeout, ExecutionTimeout)):
        return MongoDBTimeoutError(error_message, error)
    else:
        return MongoDBOperationError(error_message, error)
